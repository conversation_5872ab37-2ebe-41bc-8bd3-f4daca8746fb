// Copyright Epic Games, Inc. All Rights Reserved.

//{{NO_DEPENDENCIES}}
// Microsoft Visual C++ generated include file.
// Used by PCLaunch.rc
//
#define IDD_OK                          101
#define IDD_YESNO                       102
#define IDD_OKCANCEL                    103
#define IDD_YESNOCANCEL                 104
#define IDD_CANCELRETRYCONTINUE         105
#define IDD_YESNO2ALL                   106
#define IDD_YESNO2ALLCANCEL             107
#define IDD_YESNOYESTOALL               108
#define IDICON_UEGame                   123
#define IDC_OK                          1000
#define IDC_YESTOALL                    1001
#define IDC_NOTOALL                     1002
#define IDC_YES                         1003
#define IDC_NO_B                        1004
#define IDC_MESSAGE                     1005
#define IDC_CANCEL                      1006
#define IDC_RETRY                       1007
#define IDC_CONTINUE                    1008

#define IDR_ACCEL1                      101
#define IDC_COPY                        1009

#define ID_HASHFILE                     1010

// Next default values for new objects
// 
#ifdef APSTUDIO_INVOKED
#ifndef APSTUDIO_READONLY_SYMBOLS
#define _APS_NEXT_RESOURCE_VALUE        109
#define _APS_NEXT_COMMAND_VALUE         40001
#define _APS_NEXT_CONTROL_VALUE         1010
#define _APS_NEXT_SYMED_VALUE           101
#endif
#endif
