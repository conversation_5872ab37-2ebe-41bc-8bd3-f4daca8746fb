<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>NSMicrophoneUsageDescription</key>
	<string>UnrealEditor requires microphone access for media capture</string>
	<key>NSCameraUsageDescription</key>
	<string>UnrealEditor requires camera access for media capture</string>
	<key>NSAppTransportSecurity</key>
	<dict>
		<key>NSAllowsArbitraryLoads</key>
		<true/>
	</dict>
	<key>CFBundleDevelopmentRegion</key>
	<string>English</string>
	<key>CFBundleDocumentTypes</key>
	<array>
		<dict>
			<key>CFBundleTypeExtensions</key>
			<array>
				<string>uproject</string>
			</array>
			<key>CFBundleTypeIconFile</key>
			<string>UProject.icns</string>
			<key>CFBundleTypeName</key>
			<string>Unreal Project</string>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>LSItemContentTypes</key>
			<array>
				<string>com.epicgames.uproject</string>
			</array>
			<key>LSTypeIsPackage</key>
			<integer>0</integer>
		</dict>
	</array>
	<key>UTExportedTypeDeclarations</key>
	<array>
		<dict>
			<key>UTTypeConformsTo</key>
			<array>
				<string>public.source-code</string>
			</array>
			<key>UTTypeDescription</key>
			<string>Unreal Project</string>
			<key>UTTypeIdentifier</key>
			<string>com.epicgames.uproject</string>
			<key>UTTypeTagSpecification</key>
			<dict>
				<key>public.filename-extension</key>
				<array>
					<string>uproject</string>
				</array>
			</dict>
		</dict>
	</array>
	<key>CFBundleExecutable</key>
	<string>${EXECUTABLE_NAME}</string>
	<key>CFBundleIconFile</key>
	<string>${ICON_NAME}</string>
	<key>CFBundleIdentifier</key>
	<string>${APP_NAME}</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleName</key>
	<string>Unreal Editor</string>
	<key>CFBundleDisplayName</key>
	<string>Unreal Editor</string>
	<key>CFBundlePackageType</key>
	<string>APPL</string>
	<key>CFBundleShortVersionString</key>
	<string>${BUNDLE_VERSION}</string>
	<key>CFBundleSignature</key>
	<string>????</string>
	<key>CFBundleVersion</key>
	<string>${BUNDLE_VERSION}</string>
	<key>LSMinimumSystemVersion</key>
	<string>${MACOSX_DEPLOYMENT_TARGET}</string>
	<key>NSPrincipalClass</key>
	<string>NSApplication</string>
	<key>NSHighResolutionCapable</key>
	<true/>
	<key>NSHighResolutionMagnifyAllowed</key>
	<false/>
	<key>NSMicrophoneUsageDescription</key>
	<string>UE needs permission to use the microphone in order to use voice chat.</string>
</dict>
</plist>
